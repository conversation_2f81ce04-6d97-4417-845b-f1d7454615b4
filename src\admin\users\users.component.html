<div class="p-4" style="background: #f9f9f9cc; border-radius: 12px;">
  <div class="d-flex justify-content-between align-items-center mb-4 border-bottom pb-2">
    <div>
      <h5 class="mb-1 fw-bold">Manage Users</h5>
      <p class="text-muted mb-0">View and manage user accounts</p>
    </div>
    <div class="d-flex gap-2 align-items-center">
      <select
        class="form-select"
        style="width: auto;"
        [(ngModel)]="pageSize"
        (change)="onPageSizeChange()">
        <option value="5">5 per page</option>
        <option value="10">10 per page</option>
        <option value="25">25 per page</option>
        <option value="50">50 per page</option>
      </select>
    </div>
  </div>

  <div *ngIf="isLoading" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-2 text-muted">Loading users...</p>
  </div>

  <div *ngIf="errorMessage && !isLoading" class="alert alert-danger" role="alert">
    <i class="bi bi-exclamation-triangle me-2"></i>
    {{ errorMessage }}
    <button class="btn btn-sm btn-outline-danger ms-2" (click)="loadUsers()">
      <i class="bi bi-arrow-clockwise me-1"></i>Retry
    </button>
  </div>

  <div *ngIf="!isLoading && !errorMessage">
    <div class="d-flex justify-content-between align-items-center mb-3">
      <div class="text-muted">
        <small>
          Showing {{ getStartIndex() }} to {{ getEndIndex() }} of {{ totalCount }} users
        </small>
      </div>
    </div>

    <!-- Table View -->
    <div class="table-responsive">
      <table class="table">
        <thead>
          <tr>
            <th scope="col">#</th>
            <th scope="col">User</th>
            <th scope="col">Email</th>
            <th scope="col">Newsletter</th>
            <th scope="col">Roles</th>
            <th scope="col">Status</th>
            <!-- <th scope="col">Actions</th> -->
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let user of users; let i = index">
            <td>{{ getStartIndex() + i }}</td>
            <td>
              <div class="d-flex align-items-center">
                <div class="avatar-circle me-2">
                  {{ (user.firstName || user.userName)?.charAt(0)?.toUpperCase() || 'U' }}
                </div>
                <div>
                  <div class="fw-semibold">{{ user.firstName || 'N/A' }} {{ user.lastName || '' }}</div>
                  <small class="text-muted">{{ '@' + (user.userName || 'N/A') }}</small>
                </div>
              </div>
            </td>
            <td>{{ user.email || 'N/A' }}</td>
            <td>
              <div class="form-check form-switch">
                <input
                  class="form-check-input"
                  type="checkbox"
                  [checked]="user.newsletter"
                  [disabled]="isUserUpdating(user.id)"
                  (change)="toggleNewsletter(user)"
                  [id]="'newsletter-table-' + user.id">
                <label class="form-check-label" [for]="'newsletter-table-' + user.id">
                  <span *ngIf="!isUserUpdating(user.id)">{{ user.newsletter ? 'Yes' : 'No' }}</span>
                  <span *ngIf="isUserUpdating(user.id)" class="text-muted">
                    <i class="bi bi-arrow-repeat spin me-1"></i>Updating...
                  </span>
                </label>
              </div>
            </td>
            <td>{{ user.role || 'N/A' }}</td>
            <td>
              <div class="form-check form-switch">
                <input
                  class="form-check-input status-toggle"
                  type="checkbox"
                  [checked]="user.isActive"
                  [disabled]="isUserUpdating(user.id)"
                  (change)="toggleUserStatus(user)"
                  [id]="'status-table-' + user.id">
                <label class="form-check-label" [for]="'status-table-' + user.id">
                  <span *ngIf="!isUserUpdating(user.id)" class="badge" [class.active-badge]="user.isActive" [class.inactive-badge]="!user.isActive">
                    {{ user.isActive ? 'Active' : 'Inactive' }}
                  </span>
                  <span *ngIf="isUserUpdating(user.id)" class="badge bg-secondary">
                    <i class="bi bi-arrow-repeat spin me-1"></i>Updating...
                  </span>
                </label>
              </div>
            </td>
            <!-- <td>
              <div class="btn-group btn-group-sm" role="group">
                <button type="button" class="btn btn-outline-danger" title="Delete User">
                  <i class="bi bi-trash"></i>
                </button>
              </div>
            </td> -->
          </tr>
        </tbody>
      </table>
    </div>

    <div *ngIf="users.length === 0" class="text-center py-5">
      <i class="bi bi-people display-1 text-muted"></i>
      <h4 class="mt-3">No Users Found</h4>
      <p class="text-muted">There are no users to display at the moment.</p>
    </div>

    <nav *ngIf="totalPages > 1" aria-label="Users pagination" class="mt-4">
      <ul class="pagination justify-content-center">
        <li class="page-item" [class.disabled]="pageNumber === 1">
          <button class="page-link" (click)="prevPage()" [disabled]="pageNumber === 1">
            <i class="bi bi-chevron-left"></i>
            Previous
          </button>
        </li>

        <li *ngFor="let page of getPaginationArray()"
            class="page-item"
            [class.active]="page === pageNumber">
          <button class="page-link" (click)="goToPage(page)">
            {{ page }}
          </button>
        </li>

        <li class="page-item" [class.disabled]="pageNumber === totalPages">
          <button class="page-link" (click)="nextPage()" [disabled]="pageNumber === totalPages">
            Next
            <i class="bi bi-chevron-right"></i>
          </button>
        </li>
      </ul>
    </nav>
  </div>
</div>
