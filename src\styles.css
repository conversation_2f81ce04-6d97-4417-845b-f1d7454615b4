:root {
  --darkgold-color: #bc8710;
  --goldnew-color: #c1a407;
}
* {
  margin: 0;
  padding: 0;
  font-family: 'Inter', sans-serif;
}

html,
body,
#__next {
  height: 100%;
  width: 100%;
  border: 0;
  margin: 0px;
  box-sizing: border-box;
}

  /* rgb(84, 21, 173); */


.container {
  max-width: 1200px !important;
}


.montserrat-underline-100 {
  font-family: "Montserrat Underline", sans-serif;
  font-optical-sizing: auto;
  font-weight: 100;
  font-style: normal;
}


@font-face {
  font-family: "RussoOne-Regular";
  src: url("../public/fonts/LemonMilk.otf") format("truetype");
  font-size: medium;
  font-weight: 400;
}



:root {
  --purple-color: #a67c1d;
  --gold-color: #BC8710;
}
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
}
h1 {
  font-size: 60px !important;
  line-height: 68px !important;
}
h2 {
  font-size: 42px !important;
  line-height: 50px !important;
}
h3 {
  font-size: 32px !important;
  line-height: 42px !important;
}
h4 {
  font-size: 28px !important;
  line-height: 35px !important;
}
h5 {
  font-size: 25px !important;
  line-height: 33px !important;
}
h6 {
  font-size: 20px !important;
  line-height: 27px !important;
}
p {
  font-size: 16px;
}
/* hero section css */
.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    border: 1px solid rgb(255 255 255 / 50%);
    border-radius: 30px;
    padding: 7px 20px;
    margin-bottom: 10px;
    color: #bd8e0e;
    background: #fff;
    font-weight: 600;
}
.hero-content {
  text-align: center;
  z-index: 2;
  max-width: 800px;
  padding: 100px 0 20px;
}
.hero-title {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 15px;
  color: white;
  display: flex;
  justify-content: center;
  gap: 1rem;
}
.hero-description {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 15px;
  line-height: 25px;
  margin-left: auto;
  margin-right: auto;
}
.hero-stats .stat-item {
    position: relative;
}
.hero-stats .stat-item:not(:last-child):after {
    position: absolute;
    content: '';
    background: linear-gradient(45deg, #ffffff, transparent);
    height: 100%;
    width: 1px;
    right: -25px;
    top: 0;
}
.hero-stats {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 3rem;
  margin-top: 3rem;
}
section {
  padding: 40px 0;
}
.form-input,
.form-textarea {
  padding: 10px 20px 10px 10px;
  border: 1px solid #9e9e9e;
  border-radius: 10px;
  font-size: 15px;
  transition: all 0.3s ease;
  background: white;
}
.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--gold-color);
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.1);
}
.form-input::placeholder, .form-textarea::placeholder {
  font-size: 15px;
}
.form-label {
  font-size: 14px;
}
.modal-body input, .modal-body textarea {
    font-size: 14px;
}
.form-control:focus {
  box-shadow: none;
  border-color: var(--darkgold-color);
}

/* table css */
table {
  width: 100%;
  border-collapse: collapse;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}
table th, table td {
  text-align: left;
  padding: 12px !important;
  word-break: break-word;
  white-space: normal;
}
table th {
  background-color: #bc87101f !important;
    color: #000000;
    font-weight: 600;
}
table tr td {
  font-size: 14px;
  vertical-align: middle;
}
table tbody tr:hover {
  background-color: #f7f0e259;
}

/* badge active */
.badge {
  font-size: 0.75rem;
  padding: 6px 11px;
  border-radius: 0.375rem;
  font-weight: 500;
  border-radius: 50px;
}
.active-badge {
    background-color: #05966924 !important;
    color: #008000;
}
.inactive-badge {
    background-color: #dc262624;
    color: #b91c1c; 
}

/* Avatar Circle */
.avatar-circle {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: linear-gradient(135deg, #bc8710 0%, #f4b73c 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}
.form-select {
  font-size: 14px;
}


@media (max-width: 768px) {
  footer {
    font-size: 12px;
}
  .dashboard-search input {
    max-width: 150px !important;
  }
  .avatar-circle {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
  .hero-content {
    padding: 80px 10px 20px;
  }
  .hero-badge {
      padding: 5px 10px;
      font-size: 13px;
  }
  .hero-stats .stat-item:not(:last-child):after, .logo-container {
    display: none;
  }
  .hero-title {
    margin-bottom: 10px;
}
  .hero-stats {
    gap: 1rem;
    margin-top: 20px;
    flex-wrap: wrap;
  }
  .stat-item {
    flex: 1 1 45%;
  }
  .story-text {
    text-align: center;
  }
  .orbit-1 {
    top: 0 !important;
  }
  .orbit-circle {
    animation: none !important;
  }
  .story-underline {
    margin: 0 auto 20px;
  }
  p {
    font-size: 16px;
  }
  .stat-item .stat-number {
    font-size: 25px !important;
    margin-bottom: 0 !important;
  }
  h1 {
    font-size: 35px !important;
    line-height: 42px !important;
  }
  h2 {
    font-size: 28px !important;
    line-height: 35px !important;
  }
  h3 {
    font-size: 25px !important;
    line-height: 32px !important;
  }
  h4 {
    font-size: 22px !important;
    line-height: 30px !important;
  }
  h5 {
    font-size: 20px !important;
    line-height: 27px !important;
  }
  h6 {
    font-size: 20px !important;
    line-height: 27px !important;
  }
  .hero-title {
    font-size: 3rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  .section-header {
    margin: 0 0 20px !important;
  }
}