import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ILoginUser_Request, IRegisterUser_Request, IUserLogin, IAppUsers } from "../../Components/Home/Account/Register/register.component.model";
import { BehaviorSubject, map, Observable } from 'rxjs';
import { environment } from '../../Config/environments/environment';
import { PagedResult } from '../../models/bug-report.model';

export interface ILoginApiResponse {
  msg: string;
  data: {
    firstName: string;
    role: string;
    token: string;
    tokenExpiration: string;
    message: string;
  };
}

@Injectable({
  providedIn: 'root'
})
export class AccountService {
  private readonly _httpClient: HttpClient;
  private readonly baseURL = environment.apiUrl;
  public currentUserSource = new BehaviorSubject<IUserLogin | null>(this.getUserFromStorage());
  public currentUser$ = this.currentUserSource.asObservable();

  constructor(httpClient: HttpClient) {
    this._httpClient = httpClient;
  }

  private getUserFromStorage(): IUserLogin | null {
    try {
      const userString = localStorage.getItem("user");
      if (userString) {
        return JSON.parse(userString);
      }
    } catch (error) {
      console.error('Error parsing user from localStorage:', error);
      localStorage.removeItem("user");
    }
    return null;
  }

  login(model: ILoginUser_Request) {
    return this._httpClient.post<IUserLogin>(`${this.baseURL}/account/login`, model).pipe(
      map((response: IUserLogin) => {
        if (response) {
          localStorage.setItem("user", JSON.stringify(response));
          this.currentUserSource.next(response);
        }
        return response;
      })
    );
  }

  onSubmitForm(model: IRegisterUser_Request) {
    return this._httpClient.post<IUserLogin>(`${this.baseURL}/account/register`, model).pipe(
      map((response: IUserLogin) => {
        if (response) {
          localStorage.setItem("user", JSON.stringify(response));
          this.currentUserSource.next(response);
        }
        return response;
      })
    );
  }

  GetAllUser(pageNumber?: number, pageSize?: number): Observable<PagedResult<IAppUsers>> {
    let params = new HttpParams();
    if (pageNumber !== undefined) {
      params = params.set('pageNumber', pageNumber.toString());
    }
    if (pageSize !== undefined) {
      params = params.set('pageSize', pageSize.toString());
    }
    return this._httpClient.get<PagedResult<any>>(`${this.baseURL}/account`, { params });
  }

  UpdateUser(user: any): Observable<any> {
    return this._httpClient.put(`${this.baseURL}/account`, user);
  }

  loginUser(usernameOrEmail: string, password: string): Observable<ILoginApiResponse> {
    const loginRequest: ILoginUser_Request = {
      usernameOrEmail: usernameOrEmail,
      password: password
    };

    return this._httpClient.post<ILoginApiResponse>(`${this.baseURL}/account/login`, loginRequest).pipe(
      map((response: ILoginApiResponse) => {
        if (response && response.data && response.data.token) {
          const role = response.data?.role || undefined;
          console.log('Role extracted:', role, 'from data:', response.data?.role);

          const userLogin: IUserLogin = {
            firstName: response.data?.firstName || usernameOrEmail,
            token: response.data.token,
            role: role
          };
          localStorage.setItem("user", JSON.stringify(userLogin));
          this.currentUserSource.next(userLogin);
        }
        return response;
      })
    );
  }

  setCurrentUser(user: IUserLogin) {
    this.currentUserSource.next(user);
  }

  logout() {
    localStorage.removeItem("user");
    this.currentUserSource.next(null);
  }
}